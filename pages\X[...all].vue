<script setup lang="ts">
throw createError({
	statusCode: 410,
})
</script>

<template>
	<div class="flex flex-col gap-4 items-center justify-center py-28 bg-white text-primary-700 px-4 rounded-lg mt-12">
		<Icon
			name="ui:404"
			size="320px"
			class="text-primary-600"
		/>
		<div class="flex flex-col items-center">
			<p class="text-base my-4 max-w-[800px] text-center font-bold text-gray-600">
				{{ $t('error.404-title') }}
			</p>
			<NuxtLink
				to="/"
				class="mt-6 px-6 py-3 text-sm font-semibold text-white bg-primary-700 rounded-lg shadow-md hover:bg-primary-600 transition-all duration-300"
			>
				{{ $t('error.back-btn-title') }}
			</NuxtLink>
		</div>
	</div>
</template>
