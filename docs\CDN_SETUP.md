# Cloudflare CDN Setup Guide

This guide explains how to configure Cloudflare CDN for static assets in your Nuxt.js serverless application.

## Overview

The application is configured to serve static assets (JS, CSS, images) through Cloudflare CDN while the Lambda function handles dynamic content. This improves performance and reduces Lambda costs.

## Architecture

```
User Request
    ↓
Cloudflare CDN
    ↓
├── Static Assets (JS/CSS/Images) → S3 Bucket
└── Dynamic Content → AWS Lambda
```

## Setup Steps

### 1. Create S3 Bucket

Create an S3 bucket to store your static assets:

```bash
aws s3 mb s3://your-action-assets-bucket --region eu-west-1
```

### 2. Configure S3 Bucket Policy

Set up the bucket policy to allow public read access:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-action-assets-bucket/*"
    }
  ]
}
```

### 3. Configure Cloudflare

1. Add your domain to Cloudflare
2. Create a CNAME record pointing to your S3 bucket:
   ```
   Type: CNAME
   Name: assets (or cdn)
   Content: your-action-assets-bucket.s3.eu-west-1.amazonaws.com
   ```

### 4. Set Environment Variables

Add these environment variables to your deployment:

**GitHub Secrets:**
- `CDN_BASE_URL`: `https://assets.yourdomain.com`
- `S3_BUCKET`: `your-action-assets-bucket`

**Local Development (.env):**
```env
CDN_BASE_URL=https://assets.yourdomain.com
S3_BUCKET=your-action-assets-bucket
```

### 5. Deploy

Use the new deployment command that uploads assets to S3 and deploys Lambda:

```bash
npm run deploy:cdn
```

## Configuration Files

### nuxt.config.ts
- `serveStatic: false` - Disables serving static assets from Lambda
- `cdnURL` - Points to your Cloudflare CDN URL
- `storage.assets` - Configures S3 storage for assets

### serverless.yml
- Environment variables for CDN and S3 configuration

### GitHub Actions
- Builds with CDN environment variables
- Deploys using the CDN-aware deployment script

## Benefits

1. **Performance**: Static assets served from CDN edge locations
2. **Cost**: Reduced Lambda execution time and bandwidth costs
3. **Scalability**: CDN handles static asset traffic
4. **Caching**: Aggressive caching for static assets (1 year)
5. **Global**: Assets served from global CDN network

## Cache Headers

The deployment script sets appropriate cache headers:
- **Static Assets** (JS/CSS/Images): `max-age=31536000` (1 year)
- **HTML Files**: `max-age=3600` (1 hour)

## Troubleshooting

### Assets not loading
1. Check CDN_BASE_URL is correctly set
2. Verify S3 bucket permissions
3. Ensure Cloudflare CNAME is configured correctly

### Build issues
1. Ensure AWS CLI is configured
2. Check S3 bucket exists and is accessible
3. Verify environment variables are set

### Cache issues
1. Purge Cloudflare cache
2. Check cache headers in browser dev tools
3. Verify asset URLs are pointing to CDN
