#!/usr/bin/env node

/**
 * Deployment script that uploads static assets to S3 (behind Cloudflare CDN)
 * and then deploys the Lambda function
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Configuration
const S3_BUCKET = process.env.S3_BUCKET || 'your-s3-bucket-name'
const CDN_BASE_URL = process.env.CDN_BASE_URL || 'https://your-cloudflare-domain.com'
const AWS_REGION = process.env.AWS_REGION || 'eu-west-1'

console.log('🚀 Starting deployment with CDN...')

// Step 1: Build the application
console.log('📦 Building application...')
try {
  execSync('npm run build:lambda', { stdio: 'inherit' })
  console.log('✅ Build completed')
} catch (error) {
  console.error('❌ Build failed:', error.message)
  process.exit(1)
}

// Step 2: Upload static assets to S3
console.log('☁️ Uploading static assets to S3...')
try {
  const publicDir = '.output/public'
  
  if (fs.existsSync(publicDir)) {
    // Upload all files from .output/public to S3
    execSync(`aws s3 sync ${publicDir} s3://${S3_BUCKET}/ --region ${AWS_REGION} --delete --cache-control "public, max-age=31536000, immutable"`, { 
      stdio: 'inherit' 
    })
    
    // Set special cache headers for HTML files (shorter cache)
    execSync(`aws s3 cp s3://${S3_BUCKET}/ s3://${S3_BUCKET}/ --recursive --exclude "*" --include "*.html" --metadata-directive REPLACE --cache-control "public, max-age=3600" --region ${AWS_REGION}`, { 
      stdio: 'inherit' 
    })
    
    console.log('✅ Static assets uploaded to S3')
  } else {
    console.log('⚠️ No public directory found, skipping asset upload')
  }
} catch (error) {
  console.error('❌ S3 upload failed:', error.message)
  process.exit(1)
}

// Step 3: Deploy Lambda function
console.log('🚀 Deploying Lambda function...')
try {
  execSync('npm run deploy', { stdio: 'inherit' })
  console.log('✅ Lambda deployment completed')
} catch (error) {
  console.error('❌ Lambda deployment failed:', error.message)
  process.exit(1)
}

console.log('🎉 Deployment completed successfully!')
console.log(`📡 CDN URL: ${CDN_BASE_URL}`)
console.log(`🪣 S3 Bucket: ${S3_BUCKET}`)
