<script setup lang="ts">
import Paginate from '~/components/ui/pagination/Paginate.vue'
import type { Wishlist } from '~/interfaces/wishlist/list'
import { useCartStore } from '~/store/useCartStore'
import { useFavoriteStore } from '~/store/useFavoriteStore'

const cartStore = useCartStore()
const { priceFormat } = useCurrency()

const favoriteStore = useFavoriteStore()
await favoriteStore.fetch()
const list = computed(() => favoriteStore.list as Wishlist[])
const isEmptyList = computed<boolean>(() => !list.value?.length)
const loading = computed(() => favoriteStore.fetching)
const pagination = computed(() => favoriteStore.pagination)
const hasPagination = computed(() => favoriteStore.hasPagination)
const perPage = computed(() => favoriteStore.perPage)
const page = ref(1)

const onLoadMore = () => {
	favoriteStore.fetchMounted(1 + page.value)
}
</script>

<template>
	<Card class="w-full min-h-full h-full">
		<CardHeader class="max-sm:hidden">
			<span class="font-bold text-xl">{{ $t('profile.link-wish-title') }}</span>
		</CardHeader>
		<CardContent class="flex-grow h-full">
			<template v-if="loading">
				<div class="flex w-full gap-4 justify-center h-full flex-wrap">
					<Skeleton
						v-for="(_, index) in Array(perPage)"
						:key="`${_}-${index}`"
						class="w-48 h-48"
					/>
				</div>
			</template>
			<template v-else-if="isEmptyList">
				<div class="flex flex-col w-full h-full items-center gap-6">
					<Icon
						name="ui:empty-wish-list"
						class="w-full h-60 mt-20"
					/>
					<div class="flex text-lg max-w-xs text-center font-semibold">
						{{ $t('wish-list.empty-text') }}
					</div>

					<Button
						variant="default"
						class="w-1/2"
						:as-child="true"
					>
						<NuxtLinkLocale to="/">
							{{ $t('wish-list.empty-button') }}
						</NuxtLinkLocale>
					</Button>
				</div>
			</template>
			<template v-else>
				<div class="grid md:grid-cols-4 sm:grid-cols-3 xs:grid-cols-1 gap-6">
					<template
						v-for="item in list"
						:key="item.id"
					>
						<div class="flex gap-2 border rounded-lg border-gray-200 overflow-hidden xs:flex-row sm:flex-col">
							<NuxtLinkLocale
								:to="`/product/${item?.slug}`"
								class="flex w-full h-40 items-center justify-between p-6 bg-gray-100 max-sm:w-32"
							>
								<NuxtImg
									:src="item.media.cover?.[0]?.preview"
									class="w-full h-full object-contain"
									provider="backend"
								/>
							</NuxtLinkLocale>
							<div class="flex flex-col p-2 gap-3 flex-grow">
								<span class="text-sm font-medium truncate-2-line h-10">{{ item?.name }}</span>
								<span class="text-lg font-bold">{{ priceFormat(item.variance?.stock?.price?.value) }}</span>
								<div class="flex min-w-full justify-between items-center gap-2">
									<div class="flex flex-grow">
										<Button
											v-if="!cartStore.hasCart(item.productId)"
											:size="'sm'"
											class="min-w-full"
											@click.stop="cartStore.addToList({
												productId: item.productId,
												quantity: 1,
												varianceId: item.variance.varianceId,
											})"
										>
											{{ $t('wish-list.add-to-cart') }}
										</Button>
									</div>

									<div class="flex">
										<Button
											:size="'icon'"
											variant="icon"
											@click.stop="favoriteStore.removeFromList(item.productId)"
										>
											<Icon name="lucide:trash-2" />
										</Button>
									</div>
								</div>
							</div>
						</div>
					</template>
				</div>
			</template>
		</CardContent>
		<CardFooter v-if="hasPagination">
			<Paginate
				:items-per-page="pagination?.perPage"
				:total="pagination?.lastPage"
				:sibling-count="1"
				:show-edges="true"
				:default-page="pagination.page"
				@update:page="onLoadMore"
			/>
		</CardFooter>
	</Card>
</template>

<style scoped lang="scss">

</style>
